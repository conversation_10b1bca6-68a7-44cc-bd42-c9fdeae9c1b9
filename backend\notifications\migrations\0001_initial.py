# Generated by Django 5.2.4 on 2025-08-04 14:25

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('type', models.CharField(choices=[('campaign', 'Campaign'), ('evaluation', 'Evaluation'), ('system', 'System'), ('user', 'User')], max_length=20)),
                ('title_template', models.CharField(max_length=255)),
                ('message_template', models.TextField()),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='low', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_campaign_notifications', models.BooleanField(default=True, help_text='Receive email notifications for campaign events')),
                ('email_evaluation_notifications', models.BooleanField(default=True, help_text='Receive email notifications for evaluation events')),
                ('email_system_notifications', models.BooleanField(default=True, help_text='Receive email notifications for system updates')),
                ('app_campaign_notifications', models.BooleanField(default=True, help_text='Receive in-app notifications for campaign events')),
                ('app_evaluation_notifications', models.BooleanField(default=True, help_text='Receive in-app notifications for evaluation events')),
                ('app_system_notifications', models.BooleanField(default=True, help_text='Receive in-app notifications for system updates')),
                ('digest_frequency', models.CharField(choices=[('immediate', 'Immediate'), ('daily', 'Daily Digest'), ('weekly', 'Weekly Digest'), ('never', 'Never')], default='immediate', help_text='How often to receive notification digests', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Preference',
                'verbose_name_plural': 'Notification Preferences',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='Notification title', max_length=255)),
                ('message', models.TextField(help_text='Notification message content')),
                ('type', models.CharField(choices=[('campaign', 'Campaign'), ('evaluation', 'Evaluation'), ('system', 'System'), ('user', 'User')], default='system', help_text='Type of notification', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='low', help_text='Priority level of the notification', max_length=10)),
                ('is_read', models.BooleanField(default=False, help_text='Whether the notification has been read')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, help_text='When the notification was read', null=True)),
                ('related_object_type', models.CharField(blank=True, help_text="Type of related object (e.g., 'campaign', 'employee')", max_length=50, null=True)),
                ('related_object_id', models.PositiveIntegerField(blank=True, help_text='ID of the related object', null=True)),
                ('extra_data', models.JSONField(blank=True, default=dict, help_text='Additional data for the notification')),
                ('recipient', models.ForeignKey(help_text='User who will receive this notification', on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['recipient', '-created_at'], name='notificatio_recipie_a972ce_idx'), models.Index(fields=['recipient', 'is_read'], name='notificatio_recipie_4e3567_idx'), models.Index(fields=['type', '-created_at'], name='notificatio_type_36f2e6_idx')],
            },
        ),
    ]
