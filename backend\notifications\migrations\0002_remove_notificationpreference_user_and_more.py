# Generated by Django 5.2.4 on 2025-08-04 15:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0001_initial'),
        ('users', '0003_hrmanager_profile_picture'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='notificationpreference',
            name='user',
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='hr_manager',
            field=models.OneToOneField(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to='users.hrmanager'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='notification',
            name='recipient',
            field=models.ForeignKey(help_text='HR Manager who will receive this notification', on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='users.hrmanager'),
        ),
    ]
