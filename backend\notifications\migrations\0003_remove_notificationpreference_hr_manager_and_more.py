# Generated by Django 5.2.4 on 2025-08-04 23:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0002_remove_notificationpreference_user_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='notificationpreference',
            name='hr_manager',
        ),
        migrations.DeleteModel(
            name='NotificationTemplate',
        ),
        migrations.AlterField(
            model_name='notification',
            name='recipient',
            field=models.ForeignKey(help_text='User who will receive this notification', on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='NotificationPreference',
        ),
    ]
