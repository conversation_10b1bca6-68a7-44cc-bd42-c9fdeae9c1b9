import React, { useState, useRef, useEffect } from 'react';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
  UserGroupIcon,
  UserCircleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { globalSearchService } from '../../services/searchService';

const SearchButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);
  const containerRef = useRef(null);

  // No mock data. Will use real API data.

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchQuery('');
        setSearchResults([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle search with proper authentication
  useEffect(() => {
    if (searchQuery.trim()) {
      setIsSearching(true);
      globalSearchService.globalSearch(searchQuery.trim(), { limit: 10 })
        .then((results) => {
          // Transform results to match expected format
          const campaignResults = results.campaigns.map(c => ({
            id: c.id,
            title: c.title,
            description: c.description || c.objective || '',
            type: 'campaign',
            url: `/campaigns/${c.id}/workflow`,
          }));

          const employeeResults = results.employees.map(e => ({
            id: e.id,
            name: e.name,
            email: e.email,
            type: 'employee',
            url: `/employees/${e.id}`,
          }));

          const allResults = [...campaignResults, ...employeeResults];
          setSearchResults(allResults);
        })
        .catch((error) => {
          console.error('Search error:', error);
          setSearchResults([]);
        })
        .finally(() => setIsSearching(false));
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [searchQuery]);

  const handleClose = () => {
    setIsOpen(false);
    setSearchQuery('');
    setSearchResults([]);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleClose();
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  return (
    <div className={`relative w-full transition-all duration-300 ${isFocused ? 'drop-shadow-lg' : ''}`} ref={containerRef}>
      <div className="flex items-center w-full">
        <div className="relative w-full">
          <input
            ref={inputRef}
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder="Search campaigns, employees..."
            className={`transition-all duration-300 w-[480px] max-w-full pl-12 pr-4 py-3 bg-gradient-to-r from-white to-[#FDF8F3] rounded-full border-2 text-gray-700 placeholder-gray-500 focus:outline-none text-base ${
              isFocused
                ? 'border-[#B8935A] ring-2 ring-[#D4A574]/50 shadow-[0_8px_32px_0_rgba(212,165,116,0.35)] scale-[1.02]'
                : 'border-[#D4A574] shadow-[0_4px_16px_0_rgba(212,165,116,0.25)] hover:shadow-[0_6px_24px_0_rgba(212,165,116,0.30)]'
            } active:shadow-[0_2px_12px_0_rgba(212,165,116,0.40)] active:scale-[0.998]`}
          />
          <MagnifyingGlassIcon className={`absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 transition-all duration-200 ${
            isFocused ? 'text-[#8B6F47] scale-110' : 'text-[#B8935A]'
          }`} />
        </div>
        {(searchQuery.trim() || isSearching) && (
          <div className="absolute top-full left-0 right-0 mt-3 bg-gradient-to-b from-white to-[#FEFCFA] rounded-2xl shadow-[0_8px_32px_0_rgba(212,165,116,0.25)] border border-[#E8C4A0]/30 z-50 backdrop-blur-sm max-h-80 overflow-y-auto">
            {isSearching ? (
              <div className="px-6 py-8 text-center">
                <div className="animate-spin w-6 h-6 border-2 border-[#D4A574] border-t-transparent rounded-full mx-auto mb-3"></div>
                <p className="text-sm text-gray-600 font-medium">Searching...</p>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="py-3">
                {searchResults.map((result) => {
                  let IconComponent = UserGroupIcon;
                  if (result.type === 'employee') IconComponent = UserCircleIcon;
                  if (result.type === 'evaluation') IconComponent = DocumentTextIcon;
                  return (
                    <button
                      key={result.id}
                      className="w-full px-5 py-4 text-left hover:bg-gradient-to-r hover:from-[#D4A574]/8 hover:to-[#E8C4A0]/8 transition-all duration-200 border-b border-[#E8C4A0]/20 last:border-b-0 group"
                      onClick={() => {
                        window.location.href = result.url || '/';
                        setSearchQuery("");
                        setSearchResults([]);
                      }}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-gradient-to-br from-[#D4A574] to-[#B8935A] rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-200">
                          <IconComponent className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-semibold text-gray-800 truncate group-hover:text-[#8B6F47] transition-colors duration-200">
                            {result.title || result.name}
                          </p>
                          <p className="text-xs text-gray-500 truncate mt-0.5">
                            {result.type === 'campaign' ? result.description : result.type === 'employee' ? result.email : result.description}
                          </p>
                        </div>
                        <span className="text-xs text-[#B8935A] capitalize font-medium bg-[#D4A574]/10 px-2 py-1 rounded-full">
                          {result.type}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </div>
            ) : (
              <div className="px-6 py-10 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-[#E8C4A0]/20 to-[#D4A574]/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MagnifyingGlassIcon className="w-6 h-6 text-[#B8935A]" />
                </div>
                <p className="text-sm text-gray-600 font-medium">No results found</p>
                <p className="text-xs text-gray-500 mt-1">
                  Try different keywords
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchButton;
